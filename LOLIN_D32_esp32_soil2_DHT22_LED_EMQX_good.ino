#include <WiFi.h>
#include <PubSubClient.h>
#include <SimpleDHT.h>

#define soil1_moisture_pin A0
#define soil2_moisture_pin A3
#define LED_PIN 5           // 定义LED连接的5数字引脚
#define DHTPIN 15          // 定义DHT连接的15数字引脚
#define DHTTYPE DHT22     // 定义DHT类型为DHT22
SimpleDHT22 dht(DHTPIN);
/************************* WiFi Access Point *********************************/

//#define WLAN_SSID "BEEPLUS GUEST"
//#define WLAN_PASS "todaybeeplus"

// #define WLAN_SSID "NCON-TURBO-S"
// #define WLAN_PASS "lxl13926095770"

#define WLAN_SSID "TURBO-NET"
#define WLAN_PASS "13926095770"

/************************* EMQX MQTT Setting *********************************/
const char *mqttBroker = "mqtt.ceoceoo.com";
const int mqttPort = 1883;
const char *mqttUsername = "TubroLee";
const char *mqttPassword = "love_123";

// MQTT 主题
const char *ledTopic = "LOLIN_D32_HOME/LED";
const char *soil1Topic = "LOLIN_D32_HOME/soil_1";
const char *soil2Topic = "LOLIN_D32_HOME/soil_2";
const char *tempTopic = "LOLIN_D32_HOME/DHT22-temperature";
const char *humidTopic = "LOLIN_D32_HOME/DHT22-humidity";

WiFiClient espClient;
PubSubClient mqttClient(espClient);

// LED部分，MQTT控制回调函数
void callback(char* topic, byte* payload, unsigned int length) {
    // 转换 payload 为字符串
    String message;
    for (int i = 0; i < length; i++) {
        message += (char)payload[i];
    }

    // 检查主题并处理消息
    if (String(topic) == ledTopic) {
        if (message == "1") {
            digitalWrite(LED_PIN, HIGH); // 熄灭 LED
            Serial.println("LED is now OFF");
        } else if (message == "0") {
            digitalWrite(LED_PIN, LOW); // 点亮 LED
            Serial.println("LED is now ON");
        }
    }
}

void updateLedStatus() {
    digitalWrite(LED_PIN, currentLedState); // currentLedState 是 LED 的当前状态
    mqttClient.publish("LOLIN_D32_HOME/LED/status", currentLedState ? "1" : "0");
}

void setup() {
  Serial.begin(115200);
  delay(10);

  /*******************************LED部分-setup*********************************/
  pinMode(LED_PIN, OUTPUT); // 将LED引脚设置为输出
  digitalWrite(LED_PIN, LOW); // 初始状态开启LED

  Serial.println("Connecting to WiFi...");
  WiFi.begin(WLAN_SSID, WLAN_PASS);
  while (WiFi.status() != WL_CONNECTED) {
      delay(500);
      Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.print("IP address: ");
  Serial.println(WiFi.localIP());

  // LED部分
  mqttClient.setServer(mqttBroker, mqttPort);
  mqttClient.setCallback(callback);
  MQTT_connect();

}

void loop() {
    static unsigned long lastSensorPublishTime = 0;
    const long sensorPublishInterval = 2000;
    static int sensorPublishStep = 0;
    
    if (!mqttClient.connected()) {
        MQTT_connect();
    }
    mqttClient.loop();

    unsigned long currentMillis = millis();
    if (currentMillis - lastSensorPublishTime > sensorPublishInterval) {
        char msg[50];

        if (sensorPublishStep == 0) {
            int readValue1 = analogRead(soil1_moisture_pin);
            int soil1Humidity = round((1.0 - (float)(readValue1 - 1700) / (4095 - 1700)) * 100.0);
            sprintf(msg, "%d", soil1Humidity);
            Serial.print("Publishing soil_1 moisture (%): ");
            Serial.println(msg);
            mqttClient.publish(soil1Topic, msg);
        } else if (sensorPublishStep == 1) {
            int readValue2 = analogRead(soil2_moisture_pin);
            int soil2Humidity = round((1.0 - (float)(readValue2 - 1700) / (4095 - 1700)) * 100.0);
            sprintf(msg, "%d", soil2Humidity);
            Serial.print("Publishing soil_2 moisture (%): ");
            Serial.println(msg);
            mqttClient.publish(soil2Topic, msg);
        } else if (sensorPublishStep == 2) {
            float DHT22temperature = 0, DHT22humidity = 0;
            if (dht.read2(&DHT22temperature, &DHT22humidity, NULL) == SimpleDHTErrSuccess) {
                sprintf(msg, "%.2f", DHT22temperature);
                Serial.print("Publishing DHT22-temperature (°C): ");
                Serial.println(msg);
                mqttClient.publish(tempTopic, msg);
            } else {
                Serial.println("Failed to read DHT22 sensor");
            }
        } else if (sensorPublishStep == 3) {
            float DHT22humidity = 0;
            if (dht.read2(NULL, &DHT22humidity, NULL) == SimpleDHTErrSuccess) {
                sprintf(msg, "%.2f", DHT22humidity);
                Serial.print("Publishing DHT22-humidity (%): ");
                Serial.println(msg);
                mqttClient.publish(humidTopic, msg);
            } else {
                Serial.println("Failed to read DHT22 sensor");
            }
        }

        lastSensorPublishTime = currentMillis;
        sensorPublishStep = (sensorPublishStep + 1) % 4;
    }
}

// Function to connect and reconnect as necessary to the MQTT server.
// Should be called in the loop function and it will take care if connecting.
void MQTT_connect() {
  while (!mqttClient.connected()) {
    String clientId = "ESP32Client-";
    clientId += String(random(0xffff), HEX);
    Serial.print("EMQX MQTT connection...");
    if (mqttClient.connect(clientId.c_str(), mqttUsername, mqttPassword)) {
      Serial.println("connected");
      
      // 订阅所有相关主题
      mqttClient.subscribe(ledTopic);
      mqttClient.subscribe(soil1Topic);
      mqttClient.subscribe(soil2Topic);
      mqttClient.subscribe(tempTopic);
      mqttClient.subscribe(humidTopic);

    } else {
      Serial.print("failed, rc=");
      Serial.print(mqttClient.state());
      Serial.println(" try again in 5 seconds");
      delay(5000);
    }
  }
}
